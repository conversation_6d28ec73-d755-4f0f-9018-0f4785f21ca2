services:
  api:
    build:
      context: .
      dockerfile: ./Dockerfile-api
    command: ["fastapi", "dev", "api", "--host", "0.0.0.0"]
    volumes:
      - "./videos/:/videos/:rw"
    develop:
      watch:
        - action: sync
          path: ./api
          target: /app/api
        - action: sync
          path: ./worker
          target: /app/worker
        - action: rebuild
          path: poetry.lock
  worker:
    build:
      context: .
      dockerfile: ./Dockerfile-worker
    volumes:
      - "./videos/:/videos/:rw"
    develop:
      watch:
        - action: sync+restart
          path: ./worker
          target: /app/worker
        - action: rebuild
          path: poetry.lock
    environment:
      - LOG_LEVEL=debug

  streamprobe:
    ports:
      - "127.0.0.1:8001:8000"
