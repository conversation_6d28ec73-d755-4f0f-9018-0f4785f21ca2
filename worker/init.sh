#!/bin/sh

nb_gpu="$(nvidia-smi --query-gpu=name --format=csv,noheader | wc -l)"

if [ "$nb_gpu" -eq 0 ];
then
    echo "No GPU found, using CPU"
    nb_workers=1
elif [ "$nb_gpu" -eq 1 ]; then
    nb_workers=1
else
    echo "Multiple GPUs found, using ${nb_gpu} workers"
    nb_workers="${nb_gpu}"
fi

LOG_LEVEL="${LOG_LEVEL:-info}"


exec celery -A worker.app worker --concurrency="$nb_workers" --loglevel="$LOG_LEVEL"
