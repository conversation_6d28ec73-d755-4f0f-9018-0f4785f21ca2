{%- macro encoder_settings(quality) %}
    {%- set codec = quality.codec %}
    {%- set enc_type = quality.enc_type %}

    {%- if codec in ['libx264', 'libx265'] -%}
        {%- if enc_type == 'crf' -%}
            -crf {{ quality.crf }} -g {{ quality.gop }} -keyint_min {{ quality.gop }} -sc_threshold 0
        {%- elif enc_type == 'cbr' -%}
            -b:v {{ quality.bitrate }} -minrate {{ quality.bitrate }} -maxrate {{ quality.bitrate }} -bufsize {{ (quality.bitrate * 2) }} -g {{ quality.gop }} -keyint_min {{ quality.gop }} -sc_threshold 0
        {%- elif enc_type == 'vbr' -%}
            -b:v {{ quality.bitrate }} -g {{ quality.gop }} -keyint_min {{ quality.gop }} -sc_threshold 0
        {%- endif -%}
    {%- elif codec in ['h264_nvenc', 'hevc_nvenc'] -%}
        {%- if enc_type == 'crf' -%}
            -cq {{ quality.crf }} -g {{ quality.gop }} -gpu {{ gpu | default('0') }}
        {%- elif enc_type == 'cbr' -%}
            -b:v {{ quality.bitrate }} -minrate {{ quality.bitrate }} -maxrate {{ quality.bitrate }} -bufsize {{ (quality.bitrate * 2) }} -rc cbr -g {{ quality.gop }} -gpu {{ gpu | default('0') }}
        {%- elif enc_type == 'vbr' -%}
            -b:v {{ quality.bitrate }} -rc vbr -g {{ quality.gop }} -gpu {{ gpu | default('0') }}
        {%- endif -%}
    {%- elif codec in ['libsvtav1'] -%}
        {%- if enc_type == 'crf' -%}
            -crf {{ quality.crf }} -g {{ quality.gop }}
        {%- elif enc_type in ['cbr', 'vbr'] -%}
            -b:v {{ quality.bitrate }} -g {{ quality.gop }}
        {%- endif -%}
    {%- elif codec in ['av1_nvenc'] -%}
        {%- if enc_type == 'crf' -%}
            -cq {{ quality.crf }} -g {{ quality.gop }} -gpu {{ gpu | default('0') }}
        {%- elif enc_type in ['cbr', 'vbr'] -%}
            -b:v {{ quality.bitrate }} -g {{ quality.gop }} -gpu {{ gpu | default('0') }}
        {%- endif -%}
    {%- elif codec in ['aac', 'libopus', 'libmp3lame'] -%}
        -b:a {{ quality.bitrate }}
    {%- else -%}
        {# copy or unknown #}
    {%- endif -%}
{%- endmacro -%}


{%- macro filter_settings(quality, index) %}
    {%- if quality.codec != "copy" -%}
        {%- if quality.type == "video" -%}
            {%- if not quality.width and not quality.height -%}
                -filter:v:{{ index }} "fps={{ quality.fps }}"
            {%- else -%}
                -filter:v:{{ index }} "fps={{ quality.fps }},scale={{ quality.width or -1 }}:{{ quality.height or -1 }}"
            {%- endif -%}
        {%- elif quality.type == "audio" -%}
            -filter:a:{{ index }} "aresample=48000"
        {%- endif -%}
    {%- endif -%}
{%- endmacro -%}


ffmpeg -loglevel error -y -i {{ input_file }} \
{%- for v_quality in qualities | selectattr("type", "equalto", "video") %}
    -map 0:v:{{ v_quality.source_index }} {{ filter_settings(v_quality, loop.index0) }} -c:v:{{ loop.index0 }} {{ v_quality.codec }} {{ encoder_settings(v_quality) }} \
{%- endfor %}
{%- for a_quality in qualities | selectattr("type", "equalto", "audio") %}
    -map 0:a:{{ a_quality.source_index }} {{ filter_settings(a_quality, loop.index0) }} -c:a:{{ loop.index0 }}  {{ a_quality.codec }} {{ encoder_settings(a_quality) }} \
{%- endfor %}
    -f mp4 -movflags frag_keyframe+empty_moov -
