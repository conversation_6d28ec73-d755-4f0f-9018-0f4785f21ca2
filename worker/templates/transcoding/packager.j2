{% macro var_stream_map(output) -%}
  -var_stream_map "
  {%- for index in output.source_indexes -%}
    {%- if index.startswith("v:") -%}
      {{ index }},agroup:audio,name:{{ output.streams[loop.index0] }}
    {%- elif index.startswith("a:") -%}
      {{ index }},agroup:audio,name:{{ output.streams[loop.index0] }}
    {%- endif %}
    {{- " " if not loop.last else "" -}}
  {%- endfor -%}
  "
{%- endmacro -%}

ffmpeg -y -f mp4 -i - \
{%- for output in outputs -%}
  {%- if output.type in  ["hls", "dash"] %}
    {{ var_stream_map(output) }} \
  {%- else -%}
    {%- for index in output.source_indexes %}
      -map 0:{{ index }} \
    {%- endfor -%}
  {%- endif -%}
  {%- if output.type == "file" %}
    -c copy -f {{ output.format }} {{ output.file_name }}
  {%- endif -%}
  {% if output.type == "hls" %}
    -c copy -f hls -hls_time {{ output.segment_duration }} -hls_playlist_type vod -master_pl_name {{ output.playlist_name }} -hls_segment_filename {{ output.root_path }}/%v/{{ output.segment_name }}  {{ output.root_path }}/%v/{{ output.subplaylist_name }}
  {% endif %}
  {%- if not loop.last %} \{%- endif -%}
{%- endfor -%}
