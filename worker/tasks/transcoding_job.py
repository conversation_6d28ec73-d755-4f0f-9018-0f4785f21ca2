from time import sleep

# from celery import shared_task
from worker import app

from celery.signals import task_prerun, task_postrun, after_task_publish

from celery import Celery, Task
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import json
from random import randint
from datetime import datetime
from subprocess import CalledProcessError


from worker.tasks.transcoding.encoding_ffmpeg import EncodingFFmpeg
from worker.tasks.transcoding.packaging_ffmpeg import PackagingFFmpeg

from logging import getLogger


logger = getLogger(__name__)


# check bound tasks, it seems cool for handling retrying with different commands
@app.task(name="transcoding", track_started=True, bind=True)
def transcoding_job(self, encode_request_json: dict):
    input_file_name = encode_request_json.get("input_file", "unknown file")
    logger.info(f"Starting transcoding job for {input_file_name}")
    try:
        encoding = EncodingFFmpeg(encode_request_json)
        packaging_process = PackagingFFmpeg(encode_request_json).to_process()
        encoding_process = encoding.to_process()
        encoding_process.pipe(packaging_process).run()
        encoding_process.wait_all()
        logger.info(f"Transcoding job completed successfully for {input_file_name}")
    except CalledProcessError as e:
        encoding_process.debug()
        packaging_process.debug()
        raise e
    except Exception as e:
        logger.error(
            f"Unexpected error during transcoding job for {input_file_name}: {e}"
        )
        raise e


# TODO: ran in the api not worker
# @after_task_publish.connect
# def task_publish_handler(sender=None, body=None, **kwargs):
#     pass


# TODO: before task is ran
@task_prerun.connect
def task_prerun_handler(task_id=None, task=None, state=None, **kwargs):
    pass


# TODO: after task is ran
@task_postrun.connect
def task_postrun_handler(task_id=None, task=None, state=None, retval=None, **kwargs):
    pass
