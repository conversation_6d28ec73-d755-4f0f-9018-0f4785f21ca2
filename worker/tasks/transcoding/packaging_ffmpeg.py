from worker.tasks.transcoding.process import Process
from worker.templates import env as template_env
from shlex import split
from logging import getLogger

logger = getLogger(__name__)


class PackagingFFmpeg:
    def __init__(self, raw_data: dict):
        self.raw_data = raw_data
        self.process = None

    def generate_cmd(self) -> str:
        # jinja template is in worker/tasks/templates/transcoding/encoder.j2
        template = template_env.get_template("transcoding/packager.j2")

        v_name_to_index = {
            v["name"]: f"v:{i}"
            for i, v in enumerate(
                filter(lambda x: x["type"] == "video", self.raw_data["qualities"])
            )
        }
        a_name_to_index = {
            a["name"]: f"a:{i}"
            for i, a in enumerate(
                filter(lambda x: x["type"] == "audio", self.raw_data["qualities"])
            )
        }
        name_to_index = {**v_name_to_index, **a_name_to_index}

        # add inject missing data to simplify the mapping
        for output in self.raw_data["outputs"]:
            source_indexes = [name_to_index[stream] for stream in output["streams"]]
            output["source_indexes"] = source_indexes

        # Render the template with the raw data
        rendered_cmd = template.render(self.raw_data)
        logger.debug(f"Generated packaging command: \n{rendered_cmd}")

        return rendered_cmd

    def to_process(self) -> Process:
        cmd = self.generate_cmd()
        return Process(
            name="ffmpeg-packager",
            cmd=cmd,
        )
