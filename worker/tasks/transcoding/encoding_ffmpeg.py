from worker.tasks.transcoding.process import Process
from worker.templates import env as template_env
from shlex import split
from GPUtil import getAvailable  # type: ignore
from typing import Optional
from logging import getLogger
import billiard

logger = getLogger(__name__)


class EncodingFFmpeg:
    def __init__(self, raw_data: dict):
        self.raw_data = raw_data
        self.process = None
        self.__cmd: Optional[str] = None

    def __inject_defaults(self):
        # Set default values for missing fields in qualities
        for quality in self.raw_data["qualities"]:
            if quality["codec"] == "copy":
                continue
            if quality["type"] == "video":
                if "enc_type" not in quality:
                    quality["enc_type"] = "vbr"
                if "gop" not in quality:
                    quality["gop"] = 24
                if "fps" not in quality:
                    quality["fps"] = 25
                if "crf" not in quality and quality["codec"] != "copy":
                    quality["crf"] = 23

    def __inject_appropriate_codec(self):
        gpu_available = len(getAvailable()) != 0
        if gpu_available:
            # each process uses a signle GPU, can be tweaked later
            self.raw_data["gpu"] = billiard.current_process().index
        for quality in self.raw_data["qualities"]:
            match quality["codec"]:
                case "copy":
                    quality["codec"] = "copy"
                case "h264":
                    quality["codec"] = "libx264" if not gpu_available else "h264_nvenc"
                case "h265":
                    quality["codec"] = "libx265" if not gpu_available else "hevc_nvenc"
                case "av1":
                    quality["codec"] = "libsvtav1" if not gpu_available else "av1_nvenc"
                case "aac":
                    quality["codec"] = "aac"
                case "mp3":
                    quality["codec"] = "libmp3lame"
                case "opus":
                    quality["codec"] = "libopus"
                case _:
                    raise ValueError(f"Unsupported codec: {quality['codec']}")

    def generate_cmd(self) -> str:
        if self.__cmd is not None:
            return self.__cmd

        # jinja template is in worker/tasks/templates/transcoding/encoder.j2
        template = template_env.get_template("transcoding/encoder.j2")

        # Inject appropriate codec based on the raw data
        self.__inject_appropriate_codec()

        # Render the template with the raw data
        rendered_cmd = template.render(self.raw_data)

        logger.debug(f"Generated encoding command: \n{rendered_cmd}")

        self.__cmd = rendered_cmd

        return rendered_cmd

    def to_process(self) -> Process:
        cmd = self.generate_cmd()
        return Process(
            name="ffmpeg-enc",
            cmd=cmd,
        )
