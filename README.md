# FastEncoder

FastEncoder is a scalable and flexible project used to encode video files.

It uses a Master-Worker architecture to distribute the encoding tasks among multiple workers.

The master represents the API part that's going to manage incoming jobs. The worker represents the part that's going to encode the video files.


## Development

### Prerequisites


#### Install poetry

```sh
apt update
apt install pipx
pipx install poetry
pipx ensurepath
```

#### Getting started

```sh
poetry install
```

#### Bump the version

```sh
poetry version <new_version>
```

or

```sh
poetry version patch/minor/major
```

#### Launch the project

```sh
docker compose up -d --build
```
