from pydantic import (
    BaseModel,
    Field,
    StringConstraints,
    model_validator,
    field_validator,
)
from pathlib import Path
from typing import Union, Literal, List, Optional, Annotated
from enum import Enum
from api.schemas.validators import InputPath, OutputPath
from logging import getLogger

logger = getLogger(__name__)


class BaseQuality(BaseModel):
    name: str = Field(..., examples=["quality_name"], description="Name of the quality")
    source_index: Optional[int | Literal["all"]] = Field(
        default=0,
        examples=[0],
        description="Index of the source according to its type 0 for a video would indicate the first video",
    )
    type: Literal["video", "audio"] = Field(
        description="Type of the quality, either video, audio, bypass_video or bypass_audio",
        examples=["video", "audio"],
    )


class BypassVideoQuality(BaseQuality):
    type: Literal["video"]
    codec: Literal["copy"] = Field(
        ...,
        description="Codec to use for the output, copy means no re-encoding",
        examples=["copy"],
    )


class BypassAudioQuality(BaseQuality):
    type: Literal["audio"]
    codec: Literal["copy"] = Field(
        ...,
        description="Codec to use for the output, copy means no re-encoding",
        examples=["copy"],
    )


class VideoCodecs(str, Enum):
    H264 = "h264"
    H265 = "h265"
    AV1 = "av1"


class VideoQualityBase(BaseQuality):
    type: Literal["video"]
    width: Optional[int] = Field(
        default=None, ge=0, examples=[1920], description="Width of the video"
    )
    height: Optional[int] = Field(
        default=None, ge=0, examples=[1080], description="Height of the video"
    )
    fps: Optional[float] = Field(
        default=25,
        ge=0,
        examples=["25"],
        description="Frames per second of the output",
    )
    gop: Optional[int] = Field(
        default=48,
        ge=0,
        examples=["48"],
        description="Group of pictures (GOP) size for the output",
    )
    codec: Optional[VideoCodecs] = Field(
        default=VideoCodecs.H264,
        description="Codec to use for the output",
        examples=[VideoCodecs.H264, VideoCodecs.H265, VideoCodecs.AV1],
    )
    enc_type: Optional[Literal["cbr", "vbr", "crf"]] = Field(
        default="cbr",  # Always has a value, defaults to CBR
        description="Type of encoding to use, cbr for constant bitrate, vbr for variable bitrate, crf for constant rate factor",
        examples=["cbr", "vbr", "crf"],
    )


class VideoQuality(VideoQualityBase):
    # Make bitrate optional since CRF mode doesn't use it
    bitrate: Optional[int] = Field(
        default=5000000, gt=0, examples=["5000000"],
        description="Bitrate in bps (used for CBR and VBR modes)"
    )
    # Make CRF optional since CBR/VBR modes don't use it
    crf: Optional[int] = Field(
        default=23, ge=0, le=51, examples=[23],
        description="CRF value (0-51, used for CRF mode)"
    )


class AudioCodecs(str, Enum):
    AAC = "aac"
    MP3 = "mp3"
    OPUS = "opus"


class AudioQuality(BaseQuality):
    type: Literal["audio"]
    codec: Optional[AudioCodecs] = Field(
        default=AudioCodecs.AAC,
        description="Codec to use for the output",
        examples=[AudioCodecs.AAC, AudioCodecs.MP3, AudioCodecs.OPUS],
    )
    bitrate: int = Field(
        default=128000, gt=0, examples=[128000], description="Bitrate in bps"
    )


Quality = Union[
    BypassVideoQuality,
    VideoQuality,
    BypassAudioQuality,
    AudioQuality,
]


class VideoOutputFormat(str, Enum):  # TODO: add mkv, and mov
    MP4 = "mp4"
    MPEGTS = "mpegts"


class OutputFile(BaseModel):
    type: Literal["file"]
    format: VideoOutputFormat = Field(
        default=VideoOutputFormat.MP4,
        description="Output format of the file",
        examples=[VideoOutputFormat.MP4, VideoOutputFormat.MPEGTS],
    )
    file_name: OutputPath = Field(
        ...,
        examples=["/path/to/mounted/output/file.mp4"],
        description="Path of the output file",
    )

    streams: List[str] = Field(
        ...,
        examples=[["1080p"], ["1080p", "audio"]],
    )


class OutputHLS(BaseModel):
    type: Literal["hls"]
    root_path: OutputPath
    playlist_name: Annotated[str, StringConstraints(pattern=r".+\.m3u8$")] = Field(
        default="playlist.m3u8", description="Name of the playlist file"
    )
    subplaylist_name: Annotated[
        str, StringConstraints(pattern=r"^[^%]*%v[^%]*]\.m3u8$")
    ] = Field(default="playlist.m3u8", description="Name of the subplaylist file")
    segment_name: Annotated[
        str, StringConstraints(pattern=r"^[^%]*%\d*d[^.]*.ts$")
    ] = Field(
        default="segment_%03d.ts",
        description="Name of the segment files, must contain %v and %03d",
        examples=["segment_%v_%03d.ts"],
    )
    segment_duration: Optional[float] = Field(
        default=9.6,
        ge=0,
        examples=[10.0],
        description="Duration of each segment in seconds",
    )
    streams: List[str] = Field(
        ...,
        examples=[
            ["1080p", "720p", "audio"],
            ["1080p", "720p", "audio_fr", "audio_en"],
        ],
    )
    # TODO: not important for now, but could be useful later
    # language_map: Optional[dict[str, str]] = Field(
    #     default=None,
    #     description="Map of language codes to language names for audio streams",
    #     examples=[{"audio_fr": "French", "audio_en": "English"}],
    # )


# TODO: properly implement this
class OutputDASH(BaseModel):
    type: Literal["dash"]
    playlist_name: Optional[str] = Field(
        default="manifest.mpd", description="Name of the manifest file"
    )
    init_segment_name: Optional[str] = Field(
        default="init.mp4", description="Name of the initialization segment"
    )
    segment_name: str = Field(
        default="segment_$Number$.m4s", description="Name of the segment files"
    )
    segment_duration: Optional[int] = Field(
        default=None,
        ge=0,
        examples=[2],
        description="Duration of each segment in seconds",
    )
    segment_count: int


# TODO: not supported right now
Output = Union[OutputFile, OutputHLS]  # , OutputDASH]


class EncodeRequest(BaseModel):
    input_file: InputPath = Field(
        ...,
        examples=["/path/to/mounted/input/file.mp4"],
        description="Path of the input file",
    )
    qualities: List[Quality] = Field(
        ...,
        description="List of qualities to encode the video in",
    )
    outputs: List[Output] = Field(
        ...,
        description="Output format and options",
        examples=[
            [
                {
                    "type": "file",
                    "format": "mp4",
                    "file_name": "/path/to/mounted/output/file.mp4",
                    "streams": ["1080p", "audio"],
                },
                {
                    "type": "file",
                    "format": "mpegts",
                    "file_name": "/path/to/mounted/output/file.ts",
                    "streams": [
                        "2160p",
                        "1080p",
                        "720p",
                        "audio",
                        "original_video",
                        "original_audio",
                    ],
                },
            ],
        ],
    )

    # filter out unused qualities
    def model_post_init(self, context):
        used_qualities = set()
        for output in self.outputs:
            used_qualities.update(output.streams)

        self.qualities = [q for q in self.qualities if q.name in used_qualities]

    @model_validator(mode="after")
    def validate_coherence(self, info) -> "EncodeRequest":
        qualities = {q.name for q in self.qualities}  # encoded qualities
        for output in self.outputs:  # outputs
            for stream in output.streams:  # streams for each output
                if stream not in qualities:
                    raise ValueError(f"Quality {stream} is not defined")
        return self

    @field_validator("qualities")
    @classmethod
    def validate_qualities(cls, v: List[Quality]) -> List[Quality]:
        """Validate qualities list."""
        if not v:
            raise ValueError("At least one quality must be specified")

        # Check for duplicate quality names
        names = [q.name for q in v]
        if len(names) != len(set(names)):
            raise ValueError(f"Quality names must be unique")

        return v

    def expand_using_probe(self, probe: dict):
        videos = [el for el in probe.get("streams", []) if el["codec_type"] == "video"]
        audios = [el for el in probe.get("streams", []) if el["codec_type"] == "audio"]

        # expanding qualities
        mapping_name_to_new_names: dict[str, list[str]] = {}
        new_qualities = []
        for quality in self.qualities:
            if quality.source_index == "all":
                input_qualities = videos if quality.type == "video" else audios
                mapping_name_to_new_names[quality.name] = []
                for nb_qual, _ in enumerate(input_qualities):
                    qual = (
                        quality.model_copy()
                    )  # shallow by default, should be okay since we're only changing the source_index and name
                    qual.name = f"__{quality.name}_{nb_qual}"  # TODO: there might be a quality that already has this name
                    qual.source_index = nb_qual
                    mapping_name_to_new_names[quality.name].append(qual.name)
                    new_qualities.append(qual)
            else:
                new_qualities.append(quality)
                mapping_name_to_new_names[quality.name] = [
                    quality.name
                ]  # to avoid having conditions below

        self.qualities = new_qualities
        logger.debug(f"Expanded qualities: {self.qualities}")

        # expanding outputs
        for output in self.outputs:
            new_streams: list[str] = []
            for stream in output.streams:
                new_streams += mapping_name_to_new_names[stream]
            output.streams = new_streams

        logger.debug(f"Expanded outputs: {self.outputs}")
