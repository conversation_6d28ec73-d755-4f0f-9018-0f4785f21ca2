from fastapi import APIRouter, HTTPException

# from worker.tasks.transcoding_job import transcoding_job
from worker import app as celery_app

from api.schemas import EncodeRequest, JobResponse
from api.streamprobe import validated_streamprobe, StreamProbeValidationError

from logging import getLogger

logger = getLogger(__name__)

router = APIRouter()


@router.post("/transcode")
def transcoding_endpoint(encode_request: EncodeRequest) -> JobResponse:
    # TODO: run encoding job and send back the task id
    logger.info(f"Received request to encode {encode_request.input_file}")
    logger.debug(f"Input params: {encode_request.model_dump()}")
    try:
        probe = validated_streamprobe(encode_request.input_file)
        encode_request.expand_using_probe(probe)
    except StreamProbeValidationError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logger.error(f"Could not validate stream using streamprobe: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    task = celery_app.send_task(
        "transcoding",
        args=(encode_request.model_dump(),),
    )
    logger.debug(f"Task {task.id} created for transcoding job")
    return JobResponse(job_id=task.id)
