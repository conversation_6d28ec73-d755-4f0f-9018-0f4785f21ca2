from os import getenv
import requests
from api.streamprobe.exceptions import (
    StreamProbeValidationError,
)

STREAMPROBE_URL = getenv("STREAMPROBE_URL") or "http://streamprobe:8000"


def validated_streamprobe(filename: str) -> dict:
    params = {"input": filename}
    resp = requests.get(f"{STREAMPROBE_URL}/streamprobe/validated", params=params)

    body = resp.json()

    match resp.status_code:
        case 200:
            if body.get("validation_errors"):  # temporary
                raise StreamProbeValidationError(body.get("validation_errors", []))
            pass
        case 422:
            raise StreamProbeValidationError(body.get("validation_errors", []))
        case _:
            resp.raise_for_status()

    return body
