[project]
name = "fastencoder"
version = "0.1.0"
description = "Distributed system for encoding jobs"
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.10,<4.0"
dependencies = []


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
package-mode = false

[tool.poetry.group.api.dependencies]
celery = "^5.4.0"
python-dotenv = "^1.0.1"
jinja2 = "^3.1.4"
redis = "^5.2.0"
sqlalchemy = "^2.0.40"
psycopg2-binary = "^2.9.10"
fastapi = { extras = ["standard"], version = "^0.115.5" }
celery-types = "^0.23.0"

[tool.poetry.group.worker.dependencies]
celery = "^5.4.0"
python-dotenv = "^1.0.1"
jinja2 = "^3.1.4"
redis = "^5.2.0"
sqlalchemy = "^2.0.40"
psycopg2-binary = "^2.9.10"
gputil = "^1.4.0"
celery-types = "^0.23.0"

[[tool.poetry_bumpversion.replacements]]
files = [".env", ".env-template"]
search = 'VERSION={current_version}'
replace = 'VERSION={new_version}'

[[tool.poetry_bumpversion.replacements]]
files = ["worker/__init__.py", "api/__init__.py"]
search = '__version__ = "{current_version}"'
replace = '__version__ = "{new_version}"'

[tool.poetry.requires-plugins]
poetry-bumpversion = { version = ">=0.3.3,<1.0.0", extras = ["plugin"] }
